import base64
import json
import time
from datetime import datetime
from typing import List

from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session

# Import your existing modules
from gmail_utils import authenticate_gmail, get_emails, add_labels_to_email, get_emails_by_ids
from openai_utils import classify_email_label
from googleapiclient.errors import HttpError

# Import new backend modules
from database import get_db, create_tables, EmailRecord
from email_service import EmailService
from models import (
    EmailListResponse, StatsResponse, TrendData, ActivityItem, 
    SystemStatusResponse, ReclassifyRequest, EmailResponse
)

# Create FastAPI app
app = FastAPI(
    title="AI Email Tagger API",
    description="Backend API for AI-powered email classification and management",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:8000", "http://127.0.0.1:8000", "http://localhost:0"],  # React app
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database
create_tables()

# Gmail service
service = authenticate_gmail()

# Global variables (in production, use proper state management)
last_history_id = None

# Initialize Gmail watch
def initialize_gmail_watch():
    global last_history_id
    try:
        request_body = {
            'labelIds': ['INBOX'],
            'topicName': 'projects/ai-calendar-organization/topics/gmail-notifications'
        }
        resp = service.users().watch(userId='me', body=request_body).execute()
        print("Watch started, historyId:", resp['historyId'])
        last_history_id = int(resp['historyId'])
        return True
    except Exception as e:
        print(f"Failed to initialize Gmail watch: {e}")
        return False

# Initialize on startup
@app.on_event("startup")
async def startup_event():
    print("Starting AI Email Tagger API...")
    gmail_status = initialize_gmail_watch()
    print(f"Gmail watch initialized: {gmail_status}")

def fetch_new_message_ids(since_history_id: int):
    """
    Get message IDs that were added since the given historyId.
    """
    try:
        history_resp = service.users().history().list(
            userId='me',
            startHistoryId=since_history_id,
            historyTypes=['messageAdded']
        ).execute()
    except HttpError as e:
        print("Error fetching history:", e)
        return []

    ids = []
    for record in history_resp.get('history', []):
        for added in record.get('messagesAdded', []):
            ids.append(added['message']['id'])
    return ids

# ============================================================================
# WEBHOOK ENDPOINT (Your existing code integrated)
# ============================================================================

@app.post("/webhook")
async def gmail_webhook(request: Request, db: Session = Depends(get_db)):
    global last_history_id

    print('Request received')
    envelope = await request.json()

    # Decode Pub/Sub message
    msg = envelope.get("message", {})
    data_b64 = msg.get("data")
    if not data_b64:
        return {"status": "error", "detail": "No data field in message"}

    notification = json.loads(base64.b64decode(data_b64).decode("utf-8"))
    new_history_id = int(notification.get('historyId', 0))

    # Check for new changes
    if new_history_id <= last_history_id:
        return {"status": "no new messages"}

    # Get new message IDs
    new_ids = fetch_new_message_ids(last_history_id)

    # Update history pointer
    last_history_id = new_history_id

    processed = 0
    # Process each new message
    if new_ids:
        print(f'{len(new_ids)} new emails received. Processing...')
        emails = get_emails_by_ids(service, new_ids)
        for email in emails:
            start_time = time.time()
            
            # Classify the email
            result = classify_email_label(email)
            
            # Calculate processing time
            processing_time_ms = int((time.time() - start_time) * 1000)
            
            # Apply labels to Gmail
            add_labels_to_email(service, email['id'], result.labels)
            
            # Save to database
            EmailService.save_email(db, email, result.labels, processing_time_ms)
            
            processed += 1
            print(f"Added Email: [{email.get('subject')} ({email['id']})] to {result}")
    else:
        print('No new emails')
    
    return {"status": "success", "processed": processed}

# ============================================================================
# API ENDPOINTS FOR FRONTEND
# ============================================================================

@app.get("/emails/classified", response_model=EmailListResponse)
async def get_classified_emails(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=100),
    search: str = Query(None),
    db: Session = Depends(get_db)
):
    """Get paginated list of classified emails"""
    return EmailService.get_classified_emails(db, page, limit, search)

@app.get("/emails/stats", response_model=StatsResponse)
async def get_stats(db: Session = Depends(get_db)):
    """Get email classification statistics"""
    return EmailService.get_stats(db)

@app.get("/emails/by-label/{label}", response_model=List[EmailResponse])
async def get_emails_by_label(label: str, db: Session = Depends(get_db)):
    """Get emails filtered by a specific label"""
    return EmailService.get_emails_by_label(db, label)

@app.get("/emails/recent", response_model=List[ActivityItem])
async def get_recent_activity(
    timeframe: str = Query("24h", regex="^(24h|7d|30d)$"),
    db: Session = Depends(get_db)
):
    """Get recent email classification activity"""
    return EmailService.get_recent_activity(db, timeframe)

@app.get("/emails/trends", response_model=List[TrendData])
async def get_label_trends(
    days: int = Query(7, ge=1, le=30),
    db: Session = Depends(get_db)
):
    """Get label distribution trends over time"""
    return EmailService.get_label_trends(db, days)

@app.post("/emails/{email_id}/reclassify", response_model=EmailResponse)
async def reclassify_email(
    email_id: str,
    request: ReclassifyRequest,
    db: Session = Depends(get_db)
):
    """Manually reclassify an email with new labels"""
    try:
        # Update in database
        email_response = EmailService.reclassify_email(db, email_id, request.labels)

        # Update Gmail labels
        add_labels_to_email(service, email_id, request.labels)

        return email_response
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reclassify email: {str(e)}")

@app.post("/emails/fetch-existing")
async def fetch_existing_emails(
    max_results: int = Query(50, ge=1, le=200),
    query: str = Query("", description="Gmail search query (e.g., 'is:unread', 'from:example.com')"),
    db: Session = Depends(get_db)
):
    """Manually fetch existing emails from Gmail and process them"""
    try:
        print(f"Fetching up to {max_results} emails from Gmail...")

        # Fetch emails from Gmail
        emails = get_emails(service, query=query, max_results=max_results)

        if not emails:
            return {"status": "success", "message": "No emails found", "processed": 0}

        processed = 0
        for email in emails:
            # Check if email already exists in database
            existing_email = db.query(EmailRecord).filter(EmailRecord.id == email['id']).first()
            if existing_email:
                print(f"Email {email['id']} already exists, skipping...")
                continue

            start_time = time.time()

            # Classify the email
            result = classify_email_label(email)

            # Calculate processing time
            processing_time_ms = int((time.time() - start_time) * 1000)

            # Apply labels to Gmail
            add_labels_to_email(service, email['id'], result.labels)

            # Save to database
            EmailService.save_email(db, email, result.labels, processing_time_ms)

            processed += 1
            print(f"Processed Email: [{email.get('subject', 'No Subject')} ({email['id']})] -> {result.labels}")

        return {
            "status": "success",
            "message": f"Successfully processed {processed} emails",
            "processed": processed,
            "total_found": len(emails)
        }

    except Exception as e:
        print(f"Error fetching existing emails: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch emails: {str(e)}")

@app.get("/system/status", response_model=SystemStatusResponse)
async def get_system_status(db: Session = Depends(get_db)):
    """Get system health and status information"""
    
    # Check Gmail connection
    gmail_connected = True
    try:
        service.users().getProfile(userId='me').execute()
    except:
        gmail_connected = False
    
    # Check OpenAI connection (simplified)
    openai_connected = True  # Assume connected if no errors
    
    # Get last processed email
    last_email = db.query(EmailRecord).order_by(EmailRecord.processed_at.desc()).first()
    last_processed = last_email.processed_at if last_email else None
    
    # Count emails processed today
    today = datetime.utcnow().date()
    emails_today = db.query(EmailRecord).filter(
        EmailRecord.processed_at >= datetime.combine(today, datetime.min.time())
    ).count()
    
    return SystemStatusResponse(
        status="active" if gmail_connected and openai_connected else "degraded",
        gmail_connected=gmail_connected,
        openai_connected=openai_connected,
        last_processed=last_processed,
        emails_processed_today=emails_today
    )

# ============================================================================
# HEALTH CHECK ENDPOINTS
# ============================================================================

@app.get("/health")
async def health_check():
    """Simple health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AI Email Tagger API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

# For running locally
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)

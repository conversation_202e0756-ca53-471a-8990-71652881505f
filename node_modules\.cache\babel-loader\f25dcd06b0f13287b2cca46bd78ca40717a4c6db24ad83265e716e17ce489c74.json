{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\AI Mail tagger\\\\src\\\\components\\\\EmailList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { emailAPI } from '../services/api';\nimport { Mail, Search, Filter, ChevronLeft, ChevronRight, Tag, Calendar, User } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EmailList = () => {\n  _s();\n  const [emails, setEmails] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLabel, setSelectedLabel] = useState('all');\n  const [fetchingEmails, setFetchingEmails] = useState(false);\n  // const [sortBy, setSortBy] = useState('date'); // TODO: Implement sorting functionality\n\n  const itemsPerPage = 20;\n  const fetchEmails = async (page = 1, search = '', label = 'all') => {\n    try {\n      setLoading(true);\n      setError(null);\n      let emailData;\n      if (label === 'all') {\n        emailData = await emailAPI.getClassifiedEmails(page, itemsPerPage);\n      } else {\n        emailData = await emailAPI.getEmailsByLabel(label);\n      }\n      setEmails(emailData.emails || []);\n      setTotalPages(emailData.totalPages || 1);\n      setCurrentPage(page);\n    } catch (err) {\n      setError(err.message);\n      console.error('Error fetching emails:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchEmails(currentPage, searchTerm, selectedLabel);\n  }, [currentPage, searchTerm, selectedLabel]);\n\n  // Use only real emails from the backend - no sample data fallback\n  const displayEmails = emails;\n  const getLabelColor = label => {\n    const colors = {\n      work: 'bg-blue-100 text-blue-800',\n      personal: 'bg-green-100 text-green-800',\n      spam: 'bg-red-100 text-red-800',\n      important: 'bg-yellow-100 text-yellow-800',\n      promotional: 'bg-purple-100 text-purple-800',\n      social: 'bg-pink-100 text-pink-800',\n      family: 'bg-indigo-100 text-indigo-800',\n      shopping: 'bg-orange-100 text-orange-800',\n      tech: 'bg-cyan-100 text-cyan-800'\n    };\n    return colors[label] || 'bg-gray-100 text-gray-800';\n  };\n  const formatDate = date => {\n    const now = new Date();\n    const emailDate = new Date(date);\n    const diffInHours = (now - emailDate) / (1000 * 60 * 60);\n    if (diffInHours < 24) {\n      return emailDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffInHours < 168) {\n      // 7 days\n      return emailDate.toLocaleDateString([], {\n        weekday: 'short'\n      });\n    } else {\n      return emailDate.toLocaleDateString([], {\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n  };\n  const handlePageChange = newPage => {\n    if (newPage >= 1 && newPage <= totalPages) {\n      setCurrentPage(newPage);\n    }\n  };\n  const uniqueLabels = [...new Set(displayEmails.flatMap(email => email.labels))];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 border-b\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Classified Emails\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500\",\n            children: [displayEmails.length, \" emails\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search emails...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            className: \"h-4 w-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedLabel,\n            onChange: e => setSelectedLabel(e.target.value),\n            className: \"border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Labels\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), uniqueLabels.map(label => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: label,\n              children: label.charAt(0).toUpperCase() + label.slice(1)\n            }, label, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divide-y divide-gray-200\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mt-2\",\n          children: \"Loading emails...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center text-red-600\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Error loading emails: \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this) : displayEmails.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(Mail, {\n          className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No emails found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this) : displayEmails.map(email => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 hover:bg-gray-50 transition-colors cursor-pointer ${!email.isRead ? 'bg-blue-50' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  className: \"h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: email.senderName || email.sender\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 23\n                }, this), !email.isRead && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  className: \"h-3 w-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: formatDate(email.timestamp)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `text-sm mb-1 ${!email.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'}`,\n              children: email.subject\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n              children: email.snippet\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-1\",\n              children: email.labels.map((label, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLabelColor(label)}`,\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  className: \"h-3 w-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 25\n                }, this), label]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 15\n        }, this)\n      }, email.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-t flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500\",\n        children: [\"Page \", currentPage, \" of \", totalPages]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handlePageChange(currentPage - 1),\n          disabled: currentPage === 1,\n          className: \"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handlePageChange(currentPage + 1),\n          disabled: currentPage === totalPages,\n          className: \"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(ChevronRight, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(EmailList, \"i33Ib+kmplDdM0Db50lWyHDcirw=\");\n_c = EmailList;\nexport default EmailList;\nvar _c;\n$RefreshReg$(_c, \"EmailList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "emailAPI", "Mail", "Search", "Filter", "ChevronLeft", "ChevronRight", "Tag", "Calendar", "User", "jsxDEV", "_jsxDEV", "EmailList", "_s", "emails", "setEmails", "loading", "setLoading", "error", "setError", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLabel", "fetchingEmails", "setFetchingEmails", "itemsPerPage", "fetchEmails", "page", "search", "label", "emailData", "getClassifiedEmails", "getEmailsByLabel", "err", "message", "console", "displayEmails", "getLabelColor", "colors", "work", "personal", "spam", "important", "promotional", "social", "family", "shopping", "tech", "formatDate", "date", "now", "Date", "emailDate", "diffInHours", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "month", "day", "handlePageChange", "newPage", "uniqueLabels", "Set", "flatMap", "email", "labels", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "type", "placeholder", "value", "onChange", "e", "target", "map", "char<PERSON>t", "toUpperCase", "slice", "isRead", "sender<PERSON>ame", "sender", "timestamp", "subject", "snippet", "index", "id", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/projects/AI Mail tagger/src/components/EmailList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { emailAPI } from '../services/api';\nimport { Mail, Search, Filter, ChevronLeft, ChevronRight, Tag, Calendar, User } from 'lucide-react';\n\nconst EmailList = () => {\n  const [emails, setEmails] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLabel, setSelectedLabel] = useState('all');\n  const [fetchingEmails, setFetchingEmails] = useState(false);\n  // const [sortBy, setSortBy] = useState('date'); // TODO: Implement sorting functionality\n\n  const itemsPerPage = 20;\n\n  const fetchEmails = async (page = 1, search = '', label = 'all') => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let emailData;\n      if (label === 'all') {\n        emailData = await emailAPI.getClassifiedEmails(page, itemsPerPage);\n      } else {\n        emailData = await emailAPI.getEmailsByLabel(label);\n      }\n\n      setEmails(emailData.emails || []);\n      setTotalPages(emailData.totalPages || 1);\n      setCurrentPage(page);\n    } catch (err) {\n      setError(err.message);\n      console.error('Error fetching emails:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchEmails(currentPage, searchTerm, selectedLabel);\n  }, [currentPage, searchTerm, selectedLabel]);\n\n  // Use only real emails from the backend - no sample data fallback\n  const displayEmails = emails;\n\n  const getLabelColor = (label) => {\n    const colors = {\n      work: 'bg-blue-100 text-blue-800',\n      personal: 'bg-green-100 text-green-800',\n      spam: 'bg-red-100 text-red-800',\n      important: 'bg-yellow-100 text-yellow-800',\n      promotional: 'bg-purple-100 text-purple-800',\n      social: 'bg-pink-100 text-pink-800',\n      family: 'bg-indigo-100 text-indigo-800',\n      shopping: 'bg-orange-100 text-orange-800',\n      tech: 'bg-cyan-100 text-cyan-800',\n    };\n    return colors[label] || 'bg-gray-100 text-gray-800';\n  };\n\n  const formatDate = (date) => {\n    const now = new Date();\n    const emailDate = new Date(date);\n    const diffInHours = (now - emailDate) / (1000 * 60 * 60);\n\n    if (diffInHours < 24) {\n      return emailDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    } else if (diffInHours < 168) { // 7 days\n      return emailDate.toLocaleDateString([], { weekday: 'short' });\n    } else {\n      return emailDate.toLocaleDateString([], { month: 'short', day: 'numeric' });\n    }\n  };\n\n  const handlePageChange = (newPage) => {\n    if (newPage >= 1 && newPage <= totalPages) {\n      setCurrentPage(newPage);\n    }\n  };\n\n  const uniqueLabels = [...new Set(displayEmails.flatMap(email => email.labels))];\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border\">\n      {/* Header */}\n      <div className=\"p-6 border-b\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Classified Emails</h2>\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-gray-500\">\n              {displayEmails.length} emails\n            </span>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          {/* Search */}\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search emails...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n            />\n          </div>\n\n          {/* Label Filter */}\n          <div className=\"flex items-center space-x-2\">\n            <Filter className=\"h-4 w-4 text-gray-400\" />\n            <select\n              value={selectedLabel}\n              onChange={(e) => setSelectedLabel(e.target.value)}\n              className=\"border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n            >\n              <option value=\"all\">All Labels</option>\n              {uniqueLabels.map((label) => (\n                <option key={label} value={label}>\n                  {label.charAt(0).toUpperCase() + label.slice(1)}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Email List */}\n      <div className=\"divide-y divide-gray-200\">\n        {loading ? (\n          <div className=\"p-8 text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"></div>\n            <p className=\"text-gray-500 mt-2\">Loading emails...</p>\n          </div>\n        ) : error ? (\n          <div className=\"p-8 text-center text-red-600\">\n            <p>Error loading emails: {error}</p>\n          </div>\n        ) : displayEmails.length === 0 ? (\n          <div className=\"p-8 text-center text-gray-500\">\n            <Mail className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n            <p>No emails found</p>\n          </div>\n        ) : (\n          displayEmails.map((email) => (\n            <div\n              key={email.id}\n              className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${\n                !email.isRead ? 'bg-blue-50' : ''\n              }`}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <User className=\"h-4 w-4 text-gray-400\" />\n                      <span className=\"text-sm font-medium text-gray-900\">\n                        {email.senderName || email.sender}\n                      </span>\n                      {!email.isRead && (\n                        <div className=\"w-2 h-2 bg-primary-600 rounded-full\"></div>\n                      )}\n                    </div>\n                    <div className=\"flex items-center text-gray-500\">\n                      <Calendar className=\"h-3 w-3 mr-1\" />\n                      <span className=\"text-xs\">{formatDate(email.timestamp)}</span>\n                    </div>\n                  </div>\n\n                  <h3 className={`text-sm mb-1 ${\n                    !email.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'\n                  }`}>\n                    {email.subject}\n                  </h3>\n\n                  <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                    {email.snippet}\n                  </p>\n\n                  <div className=\"flex flex-wrap gap-1\">\n                    {email.labels.map((label, index) => (\n                      <span\n                        key={index}\n                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLabelColor(label)}`}\n                      >\n                        <Tag className=\"h-3 w-3 mr-1\" />\n                        {label}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"p-4 border-t flex items-center justify-between\">\n          <div className=\"text-sm text-gray-500\">\n            Page {currentPage} of {totalPages}\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => handlePageChange(currentPage - 1)}\n              disabled={currentPage === 1}\n              className=\"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n            >\n              <ChevronLeft className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => handlePageChange(currentPage + 1)}\n              disabled={currentPage === totalPages}\n              className=\"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n            >\n              <ChevronRight className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default EmailList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpG,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC3D;;EAEA,MAAM+B,YAAY,GAAG,EAAE;EAEvB,MAAMC,WAAW,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,EAAE,EAAEC,KAAK,GAAG,KAAK,KAAK;IAClE,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIgB,SAAS;MACb,IAAID,KAAK,KAAK,KAAK,EAAE;QACnBC,SAAS,GAAG,MAAMlC,QAAQ,CAACmC,mBAAmB,CAACJ,IAAI,EAAEF,YAAY,CAAC;MACpE,CAAC,MAAM;QACLK,SAAS,GAAG,MAAMlC,QAAQ,CAACoC,gBAAgB,CAACH,KAAK,CAAC;MACpD;MAEAnB,SAAS,CAACoB,SAAS,CAACrB,MAAM,IAAI,EAAE,CAAC;MACjCS,aAAa,CAACY,SAAS,CAACb,UAAU,IAAI,CAAC,CAAC;MACxCD,cAAc,CAACW,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZnB,QAAQ,CAACmB,GAAG,CAACC,OAAO,CAAC;MACrBC,OAAO,CAACtB,KAAK,CAAC,wBAAwB,EAAEoB,GAAG,CAAC;IAC9C,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACd+B,WAAW,CAACX,WAAW,EAAEI,UAAU,EAAEE,aAAa,CAAC;EACrD,CAAC,EAAE,CAACN,WAAW,EAAEI,UAAU,EAAEE,aAAa,CAAC,CAAC;;EAE5C;EACA,MAAMe,aAAa,GAAG3B,MAAM;EAE5B,MAAM4B,aAAa,GAAIR,KAAK,IAAK;IAC/B,MAAMS,MAAM,GAAG;MACbC,IAAI,EAAE,2BAA2B;MACjCC,QAAQ,EAAE,6BAA6B;MACvCC,IAAI,EAAE,yBAAyB;MAC/BC,SAAS,EAAE,+BAA+B;MAC1CC,WAAW,EAAE,+BAA+B;MAC5CC,MAAM,EAAE,2BAA2B;MACnCC,MAAM,EAAE,+BAA+B;MACvCC,QAAQ,EAAE,+BAA+B;MACzCC,IAAI,EAAE;IACR,CAAC;IACD,OAAOT,MAAM,CAACT,KAAK,CAAC,IAAI,2BAA2B;EACrD,CAAC;EAED,MAAMmB,UAAU,GAAIC,IAAI,IAAK;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IAChC,MAAMI,WAAW,GAAG,CAACH,GAAG,GAAGE,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAExD,IAAIC,WAAW,GAAG,EAAE,EAAE;MACpB,OAAOD,SAAS,CAACE,kBAAkB,CAAC,EAAE,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACjF,CAAC,MAAM,IAAIH,WAAW,GAAG,GAAG,EAAE;MAAE;MAC9B,OAAOD,SAAS,CAACK,kBAAkB,CAAC,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC/D,CAAC,MAAM;MACL,OAAON,SAAS,CAACK,kBAAkB,CAAC,EAAE,EAAE;QAAEE,KAAK,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAU,CAAC,CAAC;IAC7E;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC,IAAIA,OAAO,IAAI,CAAC,IAAIA,OAAO,IAAI7C,UAAU,EAAE;MACzCD,cAAc,CAAC8C,OAAO,CAAC;IACzB;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC5B,aAAa,CAAC6B,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC;EAE/E,oBACE7D,OAAA;IAAK8D,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAEnD/D,OAAA;MAAK8D,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B/D,OAAA;QAAK8D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD/D,OAAA;UAAI8D,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EnE,OAAA;UAAK8D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1C/D,OAAA;YAAM8D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACpCjC,aAAa,CAACsC,MAAM,EAAC,SACxB;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnE,OAAA;QAAK8D,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9C/D,OAAA;UAAK8D,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B/D,OAAA,CAACR,MAAM;YAACsE,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FnE,OAAA;YACEqE,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,kBAAkB;YAC9BC,KAAK,EAAE1D,UAAW;YAClB2D,QAAQ,EAAGC,CAAC,IAAK3D,aAAa,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CT,SAAS,EAAC;UAAuH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNnE,OAAA;UAAK8D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C/D,OAAA,CAACP,MAAM;YAACqE,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CnE,OAAA;YACEuE,KAAK,EAAExD,aAAc;YACrByD,QAAQ,EAAGC,CAAC,IAAKzD,gBAAgB,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAClDT,SAAS,EAAC,0GAA0G;YAAAC,QAAA,gBAEpH/D,OAAA;cAAQuE,KAAK,EAAC,KAAK;cAAAR,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACtCV,YAAY,CAACkB,GAAG,CAAEpD,KAAK,iBACtBvB,OAAA;cAAoBuE,KAAK,EAAEhD,KAAM;cAAAwC,QAAA,EAC9BxC,KAAK,CAACqD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGtD,KAAK,CAACuD,KAAK,CAAC,CAAC;YAAC,GADpCvD,KAAK;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA;MAAK8D,SAAS,EAAC,0BAA0B;MAAAC,QAAA,EACtC1D,OAAO,gBACNL,OAAA;QAAK8D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/D,OAAA;UAAK8D,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FnE,OAAA;UAAG8D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,GACJ5D,KAAK,gBACPP,OAAA;QAAK8D,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3C/D,OAAA;UAAA+D,QAAA,GAAG,wBAAsB,EAACxD,KAAK;QAAA;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,GACJrC,aAAa,CAACsC,MAAM,KAAK,CAAC,gBAC5BpE,OAAA;QAAK8D,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5C/D,OAAA,CAACT,IAAI;UAACuE,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDnE,OAAA;UAAA+D,QAAA,EAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,GAENrC,aAAa,CAAC6C,GAAG,CAAEf,KAAK,iBACtB5D,OAAA;QAEE8D,SAAS,EAAE,yDACT,CAACF,KAAK,CAACmB,MAAM,GAAG,YAAY,GAAG,EAAE,EAChC;QAAAhB,QAAA,eAEH/D,OAAA;UAAK8D,SAAS,EAAC,kCAAkC;UAAAC,QAAA,eAC/C/D,OAAA;YAAK8D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B/D,OAAA;cAAK8D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C/D,OAAA;gBAAK8D,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C/D,OAAA,CAACF,IAAI;kBAACgE,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1CnE,OAAA;kBAAM8D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAChDH,KAAK,CAACoB,UAAU,IAAIpB,KAAK,CAACqB;gBAAM;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,EACN,CAACP,KAAK,CAACmB,MAAM,iBACZ/E,OAAA;kBAAK8D,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC3D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNnE,OAAA;gBAAK8D,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C/D,OAAA,CAACH,QAAQ;kBAACiE,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCnE,OAAA;kBAAM8D,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAErB,UAAU,CAACkB,KAAK,CAACsB,SAAS;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnE,OAAA;cAAI8D,SAAS,EAAE,gBACb,CAACF,KAAK,CAACmB,MAAM,GAAG,6BAA6B,GAAG,2BAA2B,EAC1E;cAAAhB,QAAA,EACAH,KAAK,CAACuB;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAELnE,OAAA;cAAG8D,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EACnDH,KAAK,CAACwB;YAAO;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAEJnE,OAAA;cAAK8D,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAClCH,KAAK,CAACC,MAAM,CAACc,GAAG,CAAC,CAACpD,KAAK,EAAE8D,KAAK,kBAC7BrF,OAAA;gBAEE8D,SAAS,EAAE,uEAAuE/B,aAAa,CAACR,KAAK,CAAC,EAAG;gBAAAwC,QAAA,gBAEzG/D,OAAA,CAACJ,GAAG;kBAACkE,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC/B5C,KAAK;cAAA,GAJD8D,KAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA7CDP,KAAK,CAAC0B,EAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8CV,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLxD,UAAU,GAAG,CAAC,iBACbX,OAAA;MAAK8D,SAAS,EAAC,gDAAgD;MAAAC,QAAA,gBAC7D/D,OAAA;QAAK8D,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,OAChC,EAACtD,WAAW,EAAC,MAAI,EAACE,UAAU;MAAA;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACNnE,OAAA;QAAK8D,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C/D,OAAA;UACEuF,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC9C,WAAW,GAAG,CAAC,CAAE;UACjD+E,QAAQ,EAAE/E,WAAW,KAAK,CAAE;UAC5BqD,SAAS,EAAC,wGAAwG;UAAAC,QAAA,eAElH/D,OAAA,CAACN,WAAW;YAACoE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACTnE,OAAA;UACEuF,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC9C,WAAW,GAAG,CAAC,CAAE;UACjD+E,QAAQ,EAAE/E,WAAW,KAAKE,UAAW;UACrCmD,SAAS,EAAC,wGAAwG;UAAAC,QAAA,eAElH/D,OAAA,CAACL,YAAY;YAACmE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjE,EAAA,CA9NID,SAAS;AAAAwF,EAAA,GAATxF,SAAS;AAgOf,eAAeA,SAAS;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
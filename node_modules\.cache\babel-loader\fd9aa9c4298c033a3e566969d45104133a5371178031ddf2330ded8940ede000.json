{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\AI Mail tagger\\\\src\\\\components\\\\EmailList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { emailAPI } from '../services/api';\nimport { Mail, Search, Filter, ChevronLeft, ChevronRight, Tag, Calendar, User } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EmailList = () => {\n  _s();\n  const [emails, setEmails] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLabel, setSelectedLabel] = useState('all');\n  const [fetchingEmails, setFetchingEmails] = useState(false);\n  // const [sortBy, setSortBy] = useState('date'); // TODO: Implement sorting functionality\n\n  const itemsPerPage = 20;\n  const fetchEmails = async (page = 1, search = '', label = 'all') => {\n    try {\n      setLoading(true);\n      setError(null);\n      let emailData;\n      if (label === 'all') {\n        emailData = await emailAPI.getClassifiedEmails(page, itemsPerPage);\n      } else {\n        emailData = await emailAPI.getEmailsByLabel(label);\n      }\n      setEmails(emailData.emails || []);\n      setTotalPages(emailData.totalPages || 1);\n      setCurrentPage(page);\n    } catch (err) {\n      setError(err.message);\n      console.error('Error fetching emails:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchEmails(currentPage, searchTerm, selectedLabel);\n  }, [currentPage, searchTerm, selectedLabel]);\n  const fetchExistingEmails = async () => {\n    try {\n      setFetchingEmails(true);\n      setError(null);\n      const result = await emailAPI.fetchExistingEmails(50, 'is:unread OR newer_than:7d');\n      if (result.processed > 0) {\n        // Refresh the email list after fetching\n        await fetchEmails(1, searchTerm, selectedLabel);\n        alert(`Successfully processed ${result.processed} emails from Gmail!`);\n      } else {\n        alert('No new emails found to process.');\n      }\n    } catch (err) {\n      setError(`Failed to fetch emails: ${err.message}`);\n      console.error('Error fetching existing emails:', err);\n    } finally {\n      setFetchingEmails(false);\n    }\n  };\n\n  // Use only real emails from the backend - no sample data fallback\n  const displayEmails = emails;\n  const getLabelColor = label => {\n    const colors = {\n      work: 'bg-blue-100 text-blue-800',\n      personal: 'bg-green-100 text-green-800',\n      spam: 'bg-red-100 text-red-800',\n      important: 'bg-yellow-100 text-yellow-800',\n      promotional: 'bg-purple-100 text-purple-800',\n      social: 'bg-pink-100 text-pink-800',\n      family: 'bg-indigo-100 text-indigo-800',\n      shopping: 'bg-orange-100 text-orange-800',\n      tech: 'bg-cyan-100 text-cyan-800'\n    };\n    return colors[label] || 'bg-gray-100 text-gray-800';\n  };\n  const formatDate = date => {\n    const now = new Date();\n    const emailDate = new Date(date);\n    const diffInHours = (now - emailDate) / (1000 * 60 * 60);\n    if (diffInHours < 24) {\n      return emailDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffInHours < 168) {\n      // 7 days\n      return emailDate.toLocaleDateString([], {\n        weekday: 'short'\n      });\n    } else {\n      return emailDate.toLocaleDateString([], {\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n  };\n  const handlePageChange = newPage => {\n    if (newPage >= 1 && newPage <= totalPages) {\n      setCurrentPage(newPage);\n    }\n  };\n  const uniqueLabels = [...new Set(displayEmails.flatMap(email => email.labels))];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 border-b\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Classified Emails\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500\",\n            children: [displayEmails.length, \" emails\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search emails...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            className: \"h-4 w-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedLabel,\n            onChange: e => setSelectedLabel(e.target.value),\n            className: \"border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Labels\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), uniqueLabels.map(label => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: label,\n              children: label.charAt(0).toUpperCase() + label.slice(1)\n            }, label, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divide-y divide-gray-200\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mt-2\",\n          children: \"Loading emails...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center text-red-600\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Error loading emails: \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this) : displayEmails.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(Mail, {\n          className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No emails found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this) : displayEmails.map(email => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 hover:bg-gray-50 transition-colors cursor-pointer ${!email.isRead ? 'bg-blue-50' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  className: \"h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: email.senderName || email.sender\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 23\n                }, this), !email.isRead && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  className: \"h-3 w-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: formatDate(email.timestamp)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `text-sm mb-1 ${!email.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'}`,\n              children: email.subject\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n              children: email.snippet\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-1\",\n              children: email.labels.map((label, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLabelColor(label)}`,\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  className: \"h-3 w-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 25\n                }, this), label]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 15\n        }, this)\n      }, email.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-t flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500\",\n        children: [\"Page \", currentPage, \" of \", totalPages]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handlePageChange(currentPage - 1),\n          disabled: currentPage === 1,\n          className: \"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handlePageChange(currentPage + 1),\n          disabled: currentPage === totalPages,\n          className: \"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(ChevronRight, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n};\n_s(EmailList, \"i33Ib+kmplDdM0Db50lWyHDcirw=\");\n_c = EmailList;\nexport default EmailList;\nvar _c;\n$RefreshReg$(_c, \"EmailList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "emailAPI", "Mail", "Search", "Filter", "ChevronLeft", "ChevronRight", "Tag", "Calendar", "User", "jsxDEV", "_jsxDEV", "EmailList", "_s", "emails", "setEmails", "loading", "setLoading", "error", "setError", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLabel", "fetchingEmails", "setFetchingEmails", "itemsPerPage", "fetchEmails", "page", "search", "label", "emailData", "getClassifiedEmails", "getEmailsByLabel", "err", "message", "console", "fetchExistingEmails", "result", "processed", "alert", "displayEmails", "getLabelColor", "colors", "work", "personal", "spam", "important", "promotional", "social", "family", "shopping", "tech", "formatDate", "date", "now", "Date", "emailDate", "diffInHours", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "month", "day", "handlePageChange", "newPage", "uniqueLabels", "Set", "flatMap", "email", "labels", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "type", "placeholder", "value", "onChange", "e", "target", "map", "char<PERSON>t", "toUpperCase", "slice", "isRead", "sender<PERSON>ame", "sender", "timestamp", "subject", "snippet", "index", "id", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/projects/AI Mail tagger/src/components/EmailList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { emailAPI } from '../services/api';\nimport { Mail, Search, Filter, ChevronLeft, ChevronRight, Tag, Calendar, User } from 'lucide-react';\n\nconst EmailList = () => {\n  const [emails, setEmails] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLabel, setSelectedLabel] = useState('all');\n  const [fetchingEmails, setFetchingEmails] = useState(false);\n  // const [sortBy, setSortBy] = useState('date'); // TODO: Implement sorting functionality\n\n  const itemsPerPage = 20;\n\n  const fetchEmails = async (page = 1, search = '', label = 'all') => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let emailData;\n      if (label === 'all') {\n        emailData = await emailAPI.getClassifiedEmails(page, itemsPerPage);\n      } else {\n        emailData = await emailAPI.getEmailsByLabel(label);\n      }\n\n      setEmails(emailData.emails || []);\n      setTotalPages(emailData.totalPages || 1);\n      setCurrentPage(page);\n    } catch (err) {\n      setError(err.message);\n      console.error('Error fetching emails:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchEmails(currentPage, searchTerm, selectedLabel);\n  }, [currentPage, searchTerm, selectedLabel]);\n\n  const fetchExistingEmails = async () => {\n    try {\n      setFetchingEmails(true);\n      setError(null);\n\n      const result = await emailAPI.fetchExistingEmails(50, 'is:unread OR newer_than:7d');\n\n      if (result.processed > 0) {\n        // Refresh the email list after fetching\n        await fetchEmails(1, searchTerm, selectedLabel);\n        alert(`Successfully processed ${result.processed} emails from Gmail!`);\n      } else {\n        alert('No new emails found to process.');\n      }\n    } catch (err) {\n      setError(`Failed to fetch emails: ${err.message}`);\n      console.error('Error fetching existing emails:', err);\n    } finally {\n      setFetchingEmails(false);\n    }\n  };\n\n  // Use only real emails from the backend - no sample data fallback\n  const displayEmails = emails;\n\n  const getLabelColor = (label) => {\n    const colors = {\n      work: 'bg-blue-100 text-blue-800',\n      personal: 'bg-green-100 text-green-800',\n      spam: 'bg-red-100 text-red-800',\n      important: 'bg-yellow-100 text-yellow-800',\n      promotional: 'bg-purple-100 text-purple-800',\n      social: 'bg-pink-100 text-pink-800',\n      family: 'bg-indigo-100 text-indigo-800',\n      shopping: 'bg-orange-100 text-orange-800',\n      tech: 'bg-cyan-100 text-cyan-800',\n    };\n    return colors[label] || 'bg-gray-100 text-gray-800';\n  };\n\n  const formatDate = (date) => {\n    const now = new Date();\n    const emailDate = new Date(date);\n    const diffInHours = (now - emailDate) / (1000 * 60 * 60);\n\n    if (diffInHours < 24) {\n      return emailDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    } else if (diffInHours < 168) { // 7 days\n      return emailDate.toLocaleDateString([], { weekday: 'short' });\n    } else {\n      return emailDate.toLocaleDateString([], { month: 'short', day: 'numeric' });\n    }\n  };\n\n  const handlePageChange = (newPage) => {\n    if (newPage >= 1 && newPage <= totalPages) {\n      setCurrentPage(newPage);\n    }\n  };\n\n  const uniqueLabels = [...new Set(displayEmails.flatMap(email => email.labels))];\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border\">\n      {/* Header */}\n      <div className=\"p-6 border-b\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Classified Emails</h2>\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-gray-500\">\n              {displayEmails.length} emails\n            </span>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          {/* Search */}\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search emails...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n            />\n          </div>\n\n          {/* Label Filter */}\n          <div className=\"flex items-center space-x-2\">\n            <Filter className=\"h-4 w-4 text-gray-400\" />\n            <select\n              value={selectedLabel}\n              onChange={(e) => setSelectedLabel(e.target.value)}\n              className=\"border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n            >\n              <option value=\"all\">All Labels</option>\n              {uniqueLabels.map((label) => (\n                <option key={label} value={label}>\n                  {label.charAt(0).toUpperCase() + label.slice(1)}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Email List */}\n      <div className=\"divide-y divide-gray-200\">\n        {loading ? (\n          <div className=\"p-8 text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"></div>\n            <p className=\"text-gray-500 mt-2\">Loading emails...</p>\n          </div>\n        ) : error ? (\n          <div className=\"p-8 text-center text-red-600\">\n            <p>Error loading emails: {error}</p>\n          </div>\n        ) : displayEmails.length === 0 ? (\n          <div className=\"p-8 text-center text-gray-500\">\n            <Mail className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n            <p>No emails found</p>\n          </div>\n        ) : (\n          displayEmails.map((email) => (\n            <div\n              key={email.id}\n              className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${\n                !email.isRead ? 'bg-blue-50' : ''\n              }`}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <User className=\"h-4 w-4 text-gray-400\" />\n                      <span className=\"text-sm font-medium text-gray-900\">\n                        {email.senderName || email.sender}\n                      </span>\n                      {!email.isRead && (\n                        <div className=\"w-2 h-2 bg-primary-600 rounded-full\"></div>\n                      )}\n                    </div>\n                    <div className=\"flex items-center text-gray-500\">\n                      <Calendar className=\"h-3 w-3 mr-1\" />\n                      <span className=\"text-xs\">{formatDate(email.timestamp)}</span>\n                    </div>\n                  </div>\n\n                  <h3 className={`text-sm mb-1 ${\n                    !email.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'\n                  }`}>\n                    {email.subject}\n                  </h3>\n\n                  <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                    {email.snippet}\n                  </p>\n\n                  <div className=\"flex flex-wrap gap-1\">\n                    {email.labels.map((label, index) => (\n                      <span\n                        key={index}\n                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLabelColor(label)}`}\n                      >\n                        <Tag className=\"h-3 w-3 mr-1\" />\n                        {label}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"p-4 border-t flex items-center justify-between\">\n          <div className=\"text-sm text-gray-500\">\n            Page {currentPage} of {totalPages}\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => handlePageChange(currentPage - 1)}\n              disabled={currentPage === 1}\n              className=\"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n            >\n              <ChevronLeft className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => handlePageChange(currentPage + 1)}\n              disabled={currentPage === totalPages}\n              className=\"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n            >\n              <ChevronRight className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default EmailList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpG,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC3D;;EAEA,MAAM+B,YAAY,GAAG,EAAE;EAEvB,MAAMC,WAAW,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,EAAE,EAAEC,KAAK,GAAG,KAAK,KAAK;IAClE,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIgB,SAAS;MACb,IAAID,KAAK,KAAK,KAAK,EAAE;QACnBC,SAAS,GAAG,MAAMlC,QAAQ,CAACmC,mBAAmB,CAACJ,IAAI,EAAEF,YAAY,CAAC;MACpE,CAAC,MAAM;QACLK,SAAS,GAAG,MAAMlC,QAAQ,CAACoC,gBAAgB,CAACH,KAAK,CAAC;MACpD;MAEAnB,SAAS,CAACoB,SAAS,CAACrB,MAAM,IAAI,EAAE,CAAC;MACjCS,aAAa,CAACY,SAAS,CAACb,UAAU,IAAI,CAAC,CAAC;MACxCD,cAAc,CAACW,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZnB,QAAQ,CAACmB,GAAG,CAACC,OAAO,CAAC;MACrBC,OAAO,CAACtB,KAAK,CAAC,wBAAwB,EAAEoB,GAAG,CAAC;IAC9C,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACd+B,WAAW,CAACX,WAAW,EAAEI,UAAU,EAAEE,aAAa,CAAC;EACrD,CAAC,EAAE,CAACN,WAAW,EAAEI,UAAU,EAAEE,aAAa,CAAC,CAAC;EAE5C,MAAMe,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFZ,iBAAiB,CAAC,IAAI,CAAC;MACvBV,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMuB,MAAM,GAAG,MAAMzC,QAAQ,CAACwC,mBAAmB,CAAC,EAAE,EAAE,4BAA4B,CAAC;MAEnF,IAAIC,MAAM,CAACC,SAAS,GAAG,CAAC,EAAE;QACxB;QACA,MAAMZ,WAAW,CAAC,CAAC,EAAEP,UAAU,EAAEE,aAAa,CAAC;QAC/CkB,KAAK,CAAC,0BAA0BF,MAAM,CAACC,SAAS,qBAAqB,CAAC;MACxE,CAAC,MAAM;QACLC,KAAK,CAAC,iCAAiC,CAAC;MAC1C;IACF,CAAC,CAAC,OAAON,GAAG,EAAE;MACZnB,QAAQ,CAAC,2BAA2BmB,GAAG,CAACC,OAAO,EAAE,CAAC;MAClDC,OAAO,CAACtB,KAAK,CAAC,iCAAiC,EAAEoB,GAAG,CAAC;IACvD,CAAC,SAAS;MACRT,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMgB,aAAa,GAAG/B,MAAM;EAE5B,MAAMgC,aAAa,GAAIZ,KAAK,IAAK;IAC/B,MAAMa,MAAM,GAAG;MACbC,IAAI,EAAE,2BAA2B;MACjCC,QAAQ,EAAE,6BAA6B;MACvCC,IAAI,EAAE,yBAAyB;MAC/BC,SAAS,EAAE,+BAA+B;MAC1CC,WAAW,EAAE,+BAA+B;MAC5CC,MAAM,EAAE,2BAA2B;MACnCC,MAAM,EAAE,+BAA+B;MACvCC,QAAQ,EAAE,+BAA+B;MACzCC,IAAI,EAAE;IACR,CAAC;IACD,OAAOT,MAAM,CAACb,KAAK,CAAC,IAAI,2BAA2B;EACrD,CAAC;EAED,MAAMuB,UAAU,GAAIC,IAAI,IAAK;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IAChC,MAAMI,WAAW,GAAG,CAACH,GAAG,GAAGE,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAExD,IAAIC,WAAW,GAAG,EAAE,EAAE;MACpB,OAAOD,SAAS,CAACE,kBAAkB,CAAC,EAAE,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACjF,CAAC,MAAM,IAAIH,WAAW,GAAG,GAAG,EAAE;MAAE;MAC9B,OAAOD,SAAS,CAACK,kBAAkB,CAAC,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC/D,CAAC,MAAM;MACL,OAAON,SAAS,CAACK,kBAAkB,CAAC,EAAE,EAAE;QAAEE,KAAK,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAU,CAAC,CAAC;IAC7E;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC,IAAIA,OAAO,IAAI,CAAC,IAAIA,OAAO,IAAIjD,UAAU,EAAE;MACzCD,cAAc,CAACkD,OAAO,CAAC;IACzB;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC5B,aAAa,CAAC6B,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC;EAE/E,oBACEjE,OAAA;IAAKkE,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAEnDnE,OAAA;MAAKkE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BnE,OAAA;QAAKkE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDnE,OAAA;UAAIkE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EvE,OAAA;UAAKkE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1CnE,OAAA;YAAMkE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACpCjC,aAAa,CAACsC,MAAM,EAAC,SACxB;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvE,OAAA;QAAKkE,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9CnE,OAAA;UAAKkE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BnE,OAAA,CAACR,MAAM;YAAC0E,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FvE,OAAA;YACEyE,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,kBAAkB;YAC9BC,KAAK,EAAE9D,UAAW;YAClB+D,QAAQ,EAAGC,CAAC,IAAK/D,aAAa,CAAC+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CT,SAAS,EAAC;UAAuH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNvE,OAAA;UAAKkE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CnE,OAAA,CAACP,MAAM;YAACyE,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CvE,OAAA;YACE2E,KAAK,EAAE5D,aAAc;YACrB6D,QAAQ,EAAGC,CAAC,IAAK7D,gBAAgB,CAAC6D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAClDT,SAAS,EAAC,0GAA0G;YAAAC,QAAA,gBAEpHnE,OAAA;cAAQ2E,KAAK,EAAC,KAAK;cAAAR,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACtCV,YAAY,CAACkB,GAAG,CAAExD,KAAK,iBACtBvB,OAAA;cAAoB2E,KAAK,EAAEpD,KAAM;cAAA4C,QAAA,EAC9B5C,KAAK,CAACyD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG1D,KAAK,CAAC2D,KAAK,CAAC,CAAC;YAAC,GADpC3D,KAAK;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvE,OAAA;MAAKkE,SAAS,EAAC,0BAA0B;MAAAC,QAAA,EACtC9D,OAAO,gBACNL,OAAA;QAAKkE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BnE,OAAA;UAAKkE,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FvE,OAAA;UAAGkE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,GACJhE,KAAK,gBACPP,OAAA;QAAKkE,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3CnE,OAAA;UAAAmE,QAAA,GAAG,wBAAsB,EAAC5D,KAAK;QAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,GACJrC,aAAa,CAACsC,MAAM,KAAK,CAAC,gBAC5BxE,OAAA;QAAKkE,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CnE,OAAA,CAACT,IAAI;UAAC2E,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDvE,OAAA;UAAAmE,QAAA,EAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,GAENrC,aAAa,CAAC6C,GAAG,CAAEf,KAAK,iBACtBhE,OAAA;QAEEkE,SAAS,EAAE,yDACT,CAACF,KAAK,CAACmB,MAAM,GAAG,YAAY,GAAG,EAAE,EAChC;QAAAhB,QAAA,eAEHnE,OAAA;UAAKkE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,eAC/CnE,OAAA;YAAKkE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnE,OAAA;cAAKkE,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CnE,OAAA;gBAAKkE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CnE,OAAA,CAACF,IAAI;kBAACoE,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1CvE,OAAA;kBAAMkE,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAChDH,KAAK,CAACoB,UAAU,IAAIpB,KAAK,CAACqB;gBAAM;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,EACN,CAACP,KAAK,CAACmB,MAAM,iBACZnF,OAAA;kBAAKkE,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC3D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNvE,OAAA;gBAAKkE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CnE,OAAA,CAACH,QAAQ;kBAACqE,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCvE,OAAA;kBAAMkE,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAErB,UAAU,CAACkB,KAAK,CAACsB,SAAS;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvE,OAAA;cAAIkE,SAAS,EAAE,gBACb,CAACF,KAAK,CAACmB,MAAM,GAAG,6BAA6B,GAAG,2BAA2B,EAC1E;cAAAhB,QAAA,EACAH,KAAK,CAACuB;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAELvE,OAAA;cAAGkE,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EACnDH,KAAK,CAACwB;YAAO;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAEJvE,OAAA;cAAKkE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAClCH,KAAK,CAACC,MAAM,CAACc,GAAG,CAAC,CAACxD,KAAK,EAAEkE,KAAK,kBAC7BzF,OAAA;gBAEEkE,SAAS,EAAE,uEAAuE/B,aAAa,CAACZ,KAAK,CAAC,EAAG;gBAAA4C,QAAA,gBAEzGnE,OAAA,CAACJ,GAAG;kBAACsE,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC/BhD,KAAK;cAAA,GAJDkE,KAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA7CDP,KAAK,CAAC0B,EAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8CV,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL5D,UAAU,GAAG,CAAC,iBACbX,OAAA;MAAKkE,SAAS,EAAC,gDAAgD;MAAAC,QAAA,gBAC7DnE,OAAA;QAAKkE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,OAChC,EAAC1D,WAAW,EAAC,MAAI,EAACE,UAAU;MAAA;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACNvE,OAAA;QAAKkE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CnE,OAAA;UACE2F,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAClD,WAAW,GAAG,CAAC,CAAE;UACjDmF,QAAQ,EAAEnF,WAAW,KAAK,CAAE;UAC5ByD,SAAS,EAAC,wGAAwG;UAAAC,QAAA,eAElHnE,OAAA,CAACN,WAAW;YAACwE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACTvE,OAAA;UACE2F,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAClD,WAAW,GAAG,CAAC,CAAE;UACjDmF,QAAQ,EAAEnF,WAAW,KAAKE,UAAW;UACrCuD,SAAS,EAAC,wGAAwG;UAAAC,QAAA,eAElHnE,OAAA,CAACL,YAAY;YAACuE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrE,EAAA,CApPID,SAAS;AAAA4F,EAAA,GAAT5F,SAAS;AAsPf,eAAeA,SAAS;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import axios from 'axios';

// Base URL for your FastAPI backend
const API_BASE_URL = 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// API endpoints that will be implemented in your FastAPI backend
export const emailAPI = {
  // Get all classified emails with pagination
  getClassifiedEmails: async (page = 1, limit = 50) => {
    const response = await api.get(`/emails/classified?page=${page}&limit=${limit}`);
    return response.data;
  },

  // Get email classification statistics
  getStats: async () => {
    const response = await api.get('/emails/stats');
    return response.data;
  },

  // Get emails by label
  getEmailsByLabel: async (label) => {
    const response = await api.get(`/emails/by-label/${label}`);
    return response.data;
  },

  // Get recent activity (last 24 hours, 7 days, etc.)
  getRecentActivity: async (timeframe = '24h') => {
    const response = await api.get(`/emails/recent?timeframe=${timeframe}`);
    return response.data;
  },

  // Get label distribution over time
  getLabelTrends: async (days = 7) => {
    const response = await api.get(`/emails/trends?days=${days}`);
    return response.data;
  },

  // Manually reclassify an email (if needed)
  reclassifyEmail: async (emailId, newLabels) => {
    const response = await api.post(`/emails/${emailId}/reclassify`, {
      labels: newLabels
    });
    return response.data;
  },

  // Get system health/status
  getSystemStatus: async () => {
    const response = await api.get('/system/status');
    return response.data;
  },

  // Fetch existing emails from Gmail
  fetchExistingEmails: async (maxResults = 50, query = '') => {
    const response = await api.post(`/emails/fetch-existing?max_results=${maxResults}&query=${encodeURIComponent(query)}`);
    return response.data;
  }
};

// Error handling interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    
    if (error.response?.status === 404) {
      throw new Error('Endpoint not found. Make sure your FastAPI backend is running.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error. Please try again later.');
    } else if (error.code === 'ECONNREFUSED') {
      throw new Error('Cannot connect to backend. Make sure FastAPI is running on port 8000.');
    }
    
    throw error;
  }
);

export default api;

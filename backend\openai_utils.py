# OpenAI utility functions for email classification
# This is a placeholder for your existing openai_utils.py file
# You should copy your actual implementation here

import openai
import os
from typing import List
from pydantic import BaseModel
from dotenv import load_dotenv

load_dotenv()

os.environ['OPENAI_API_KEY'] = os.getenv("OPENAI_API_KEY")

# Set your OpenAI API key
# openai.api_key = os.getenv("OPENAI_API_KEY")

class ClassificationResult(BaseModel):
    labels: List[str]
    confidence: str = "medium"  # high, medium, low

def classify_email_label(email_data: dict) -> ClassificationResult:
    """
    Classify an email and return appropriate labels using OpenAI
    """
    try:
        # Prepare the email content for classification
        subject = email_data.get('subject', '')
        snippet = email_data.get('snippet', '')
        sender = email_data.get('sender', '')
        
        # Create a prompt for OpenAI
        prompt = f"""
        Classify this email into one or more of the following categories:
        - work: Work-related emails, meetings, business communications
        - personal: Personal emails from friends, family, personal services
        - spam: Unwanted emails, promotional content, suspicious emails
        - important: Urgent or high-priority emails
        - promotional: Marketing emails, newsletters, offers
        - social: Social media notifications, social platforms
        - family: Family-related communications
        - shopping: E-commerce, orders, receipts, shopping-related
        - tech: Technology news, updates, technical content
        
        Email details:
        From: {sender}
        Subject: {subject}
        Content: {snippet}
        
        Return only the category names as a comma-separated list (e.g., "work,important" or "spam").
        Choose the most appropriate 1-3 categories.
        """
        
        # Call OpenAI API
        client = openai.OpenAI()
        response = client.chat.completions.create(
            model="gpt-5-nano",
            messages=[
                {"role": "system", "content": "You are an email classification assistant. Classify emails accurately into the given categories."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=50,
            temperature=0.1
        )
        
        # Parse the response
        classification_text = response.choices[0].message.content.strip().lower()
        labels = [label.strip() for label in classification_text.split(',')]
        
        # Validate labels
        valid_labels = ['work', 'personal', 'spam', 'important', 'promotional', 'social', 'family', 'shopping', 'tech']
        filtered_labels = [label for label in labels if label in valid_labels]
        
        # If no valid labels, default to 'personal'
        if not filtered_labels:
            filtered_labels = ['personal']
        
        # Determine confidence based on email characteristics
        confidence = "high"
        if any(keyword in (subject + snippet).lower() for keyword in ['urgent', 'important', 'asap']):
            if 'important' not in filtered_labels:
                filtered_labels.append('important')
        
        # Spam detection
        spam_indicators = ['winner', 'congratulations', 'click here', 'free money', 'limited time']
        if any(indicator in (subject + snippet).lower() for indicator in spam_indicators):
            filtered_labels = ['spam']
            confidence = "high"
        
        return ClassificationResult(labels=filtered_labels, confidence=confidence)
        
    except Exception as e:
        print(f"Error classifying email: {e}")
        # Return default classification on error
        return ClassificationResult(labels=['personal'], confidence="low")

def get_classification_confidence(email_data: dict, labels: List[str]) -> str:
    """
    Determine the confidence level of the classification
    """
    subject = email_data.get('subject', '').lower()
    snippet = email_data.get('snippet', '').lower()
    sender = email_data.get('sender', '').lower()
    
    # High confidence indicators
    high_confidence_indicators = {
        'work': ['meeting', 'project', 'deadline', 'office', 'team'],
        'spam': ['winner', 'congratulations', 'free', 'click here', 'limited time'],
        'important': ['urgent', 'asap', 'important', 'critical', 'emergency'],
        'shopping': ['order', 'receipt', 'shipped', 'delivery', 'purchase']
    }
    
    for label in labels:
        if label in high_confidence_indicators:
            indicators = high_confidence_indicators[label]
            if any(indicator in subject or indicator in snippet for indicator in indicators):
                return "high"
    
    # Check sender domain for confidence
    if any(domain in sender for domain in ['.com', '.org', '.edu', '.gov']):
        return "medium"
    
    return "low"

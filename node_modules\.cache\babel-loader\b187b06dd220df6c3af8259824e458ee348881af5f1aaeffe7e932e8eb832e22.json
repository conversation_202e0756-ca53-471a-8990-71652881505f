{"ast": null, "code": "import axios from 'axios';\n\n// Base URL for your FastAPI backend\nconst API_BASE_URL = 'http://localhost:8000';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// API endpoints that will be implemented in your FastAPI backend\nexport const emailAPI = {\n  // Get all classified emails with pagination\n  getClassifiedEmails: async (page = 1, limit = 50) => {\n    const response = await api.get(`/emails/classified?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n  // Get email classification statistics\n  getStats: async () => {\n    const response = await api.get('/emails/stats');\n    return response.data;\n  },\n  // Get emails by label\n  getEmailsByLabel: async label => {\n    const response = await api.get(`/emails/by-label/${label}`);\n    return response.data;\n  },\n  // Get recent activity (last 24 hours, 7 days, etc.)\n  getRecentActivity: async (timeframe = '24h') => {\n    const response = await api.get(`/emails/recent?timeframe=${timeframe}`);\n    return response.data;\n  },\n  // Get label distribution over time\n  getLabelTrends: async (days = 7) => {\n    const response = await api.get(`/emails/trends?days=${days}`);\n    return response.data;\n  },\n  // Manually reclassify an email (if needed)\n  reclassifyEmail: async (emailId, newLabels) => {\n    const response = await api.post(`/emails/${emailId}/reclassify`, {\n      labels: newLabels\n    });\n    return response.data;\n  },\n  // Get system health/status\n  getSystemStatus: async () => {\n    const response = await api.get('/system/status');\n    return response.data;\n  },\n  // Fetch existing emails from Gmail\n  fetchExistingEmails: async (maxResults = 50, query = '') => {\n    const response = await api.post(`/emails/fetch-existing?max_results=${maxResults}&query=${encodeURIComponent(query)}`);\n    return response.data;\n  }\n};\n\n// Error handling interceptor\napi.interceptors.response.use(response => response, error => {\n  var _error$response, _error$response2;\n  console.error('API Error:', error);\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 404) {\n    throw new Error('Endpoint not found. Make sure your FastAPI backend is running.');\n  } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) >= 500) {\n    throw new Error('Server error. Please try again later.');\n  } else if (error.code === 'ECONNREFUSED') {\n    throw new Error('Cannot connect to backend. Make sure FastAPI is running on port 8000.');\n  }\n  throw error;\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "headers", "emailAPI", "getClassifiedEmails", "page", "limit", "response", "get", "data", "getStats", "getEmailsByLabel", "label", "getRecentActivity", "timeframe", "getLabelTrends", "days", "reclassifyEmail", "emailId", "<PERSON><PERSON><PERSON><PERSON>", "post", "labels", "getSystemStatus", "fetchExistingEmails", "maxResults", "query", "encodeURIComponent", "interceptors", "use", "error", "_error$response", "_error$response2", "console", "status", "Error", "code"], "sources": ["C:/Users/<USER>/Documents/projects/AI Mail tagger/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Base URL for your FastAPI backend\nconst API_BASE_URL = 'http://localhost:8000';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// API endpoints that will be implemented in your FastAPI backend\nexport const emailAPI = {\n  // Get all classified emails with pagination\n  getClassifiedEmails: async (page = 1, limit = 50) => {\n    const response = await api.get(`/emails/classified?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n\n  // Get email classification statistics\n  getStats: async () => {\n    const response = await api.get('/emails/stats');\n    return response.data;\n  },\n\n  // Get emails by label\n  getEmailsByLabel: async (label) => {\n    const response = await api.get(`/emails/by-label/${label}`);\n    return response.data;\n  },\n\n  // Get recent activity (last 24 hours, 7 days, etc.)\n  getRecentActivity: async (timeframe = '24h') => {\n    const response = await api.get(`/emails/recent?timeframe=${timeframe}`);\n    return response.data;\n  },\n\n  // Get label distribution over time\n  getLabelTrends: async (days = 7) => {\n    const response = await api.get(`/emails/trends?days=${days}`);\n    return response.data;\n  },\n\n  // Manually reclassify an email (if needed)\n  reclassifyEmail: async (emailId, newLabels) => {\n    const response = await api.post(`/emails/${emailId}/reclassify`, {\n      labels: newLabels\n    });\n    return response.data;\n  },\n\n  // Get system health/status\n  getSystemStatus: async () => {\n    const response = await api.get('/system/status');\n    return response.data;\n  },\n\n  // Fetch existing emails from Gmail\n  fetchExistingEmails: async (maxResults = 50, query = '') => {\n    const response = await api.post(`/emails/fetch-existing?max_results=${maxResults}&query=${encodeURIComponent(query)}`);\n    return response.data;\n  }\n};\n\n// Error handling interceptor\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    console.error('API Error:', error);\n    \n    if (error.response?.status === 404) {\n      throw new Error('Endpoint not found. Make sure your FastAPI backend is running.');\n    } else if (error.response?.status >= 500) {\n      throw new Error('Server error. Please try again later.');\n    } else if (error.code === 'ECONNREFUSED') {\n      throw new Error('Cannot connect to backend. Make sure FastAPI is running on port 8000.');\n    }\n    \n    throw error;\n  }\n);\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAG,uBAAuB;AAE5C,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtB;EACAC,mBAAmB,EAAE,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,KAAK;IACnD,MAAMC,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,2BAA2BH,IAAI,UAAUC,KAAK,EAAE,CAAC;IAChF,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,QAAQ,EAAE,MAAAA,CAAA,KAAY;IACpB,MAAMH,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,eAAe,CAAC;IAC/C,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAE,gBAAgB,EAAE,MAAOC,KAAK,IAAK;IACjC,MAAML,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,oBAAoBI,KAAK,EAAE,CAAC;IAC3D,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAI,iBAAiB,EAAE,MAAAA,CAAOC,SAAS,GAAG,KAAK,KAAK;IAC9C,MAAMP,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,4BAA4BM,SAAS,EAAE,CAAC;IACvE,OAAOP,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAM,cAAc,EAAE,MAAAA,CAAOC,IAAI,GAAG,CAAC,KAAK;IAClC,MAAMT,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,uBAAuBQ,IAAI,EAAE,CAAC;IAC7D,OAAOT,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAQ,eAAe,EAAE,MAAAA,CAAOC,OAAO,EAAEC,SAAS,KAAK;IAC7C,MAAMZ,QAAQ,GAAG,MAAMR,GAAG,CAACqB,IAAI,CAAC,WAAWF,OAAO,aAAa,EAAE;MAC/DG,MAAM,EAAEF;IACV,CAAC,CAAC;IACF,OAAOZ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAa,eAAe,EAAE,MAAAA,CAAA,KAAY;IAC3B,MAAMf,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,gBAAgB,CAAC;IAChD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAc,mBAAmB,EAAE,MAAAA,CAAOC,UAAU,GAAG,EAAE,EAAEC,KAAK,GAAG,EAAE,KAAK;IAC1D,MAAMlB,QAAQ,GAAG,MAAMR,GAAG,CAACqB,IAAI,CAAC,sCAAsCI,UAAU,UAAUE,kBAAkB,CAACD,KAAK,CAAC,EAAE,CAAC;IACtH,OAAOlB,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACAV,GAAG,CAAC4B,YAAY,CAACpB,QAAQ,CAACqB,GAAG,CAC1BrB,QAAQ,IAAKA,QAAQ,EACrBsB,KAAK,IAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACTC,OAAO,CAACH,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EAElC,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAACtB,QAAQ,cAAAuB,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,MAAK,GAAG,EAAE;IAClC,MAAM,IAAIC,KAAK,CAAC,gEAAgE,CAAC;EACnF,CAAC,MAAM,IAAI,EAAAH,gBAAA,GAAAF,KAAK,CAACtB,QAAQ,cAAAwB,gBAAA,uBAAdA,gBAAA,CAAgBE,MAAM,KAAI,GAAG,EAAE;IACxC,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;EAC1D,CAAC,MAAM,IAAIL,KAAK,CAACM,IAAI,KAAK,cAAc,EAAE;IACxC,MAAM,IAAID,KAAK,CAAC,uEAAuE,CAAC;EAC1F;EAEA,MAAML,KAAK;AACb,CACF,CAAC;AAED,eAAe9B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
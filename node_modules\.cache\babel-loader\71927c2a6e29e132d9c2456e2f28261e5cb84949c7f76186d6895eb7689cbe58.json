{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\AI Mail tagger\\\\src\\\\components\\\\EmailList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { emailAPI } from '../services/api';\nimport { Mail, Search, Filter, ChevronLeft, ChevronRight, Tag, Calendar, User } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EmailList = () => {\n  _s();\n  const [emails, setEmails] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLabel, setSelectedLabel] = useState('all');\n  const [fetchingEmails, setFetchingEmails] = useState(false);\n  // const [sortBy, setSortBy] = useState('date'); // TODO: Implement sorting functionality\n\n  const itemsPerPage = 20;\n  const fetchEmails = async (page = 1, search = '', label = 'all') => {\n    try {\n      setLoading(true);\n      setError(null);\n      let emailData;\n      if (label === 'all') {\n        emailData = await emailAPI.getClassifiedEmails(page, itemsPerPage);\n      } else {\n        emailData = await emailAPI.getEmailsByLabel(label);\n      }\n      setEmails(emailData.emails || []);\n      setTotalPages(emailData.totalPages || 1);\n      setCurrentPage(page);\n    } catch (err) {\n      setError(err.message);\n      console.error('Error fetching emails:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchEmails(currentPage, searchTerm, selectedLabel);\n  }, [currentPage, searchTerm, selectedLabel]);\n  const fetchExistingEmails = async () => {\n    try {\n      setFetchingEmails(true);\n      setError(null);\n      const result = await emailAPI.fetchExistingEmails(50, 'is:unread OR newer_than:7d');\n      if (result.processed > 0) {\n        // Refresh the email list after fetching\n        await fetchEmails(1, searchTerm, selectedLabel);\n        alert(`Successfully processed ${result.processed} emails from Gmail!`);\n      } else {\n        alert('No new emails found to process.');\n      }\n    } catch (err) {\n      setError(`Failed to fetch emails: ${err.message}`);\n      console.error('Error fetching existing emails:', err);\n    } finally {\n      setFetchingEmails(false);\n    }\n  };\n\n  // Use only real emails from the backend - no sample data fallback\n  const displayEmails = emails;\n  const getLabelColor = label => {\n    const colors = {\n      work: 'bg-blue-100 text-blue-800',\n      personal: 'bg-green-100 text-green-800',\n      spam: 'bg-red-100 text-red-800',\n      important: 'bg-yellow-100 text-yellow-800',\n      promotional: 'bg-purple-100 text-purple-800',\n      social: 'bg-pink-100 text-pink-800',\n      family: 'bg-indigo-100 text-indigo-800',\n      shopping: 'bg-orange-100 text-orange-800',\n      tech: 'bg-cyan-100 text-cyan-800'\n    };\n    return colors[label] || 'bg-gray-100 text-gray-800';\n  };\n  const formatDate = date => {\n    const now = new Date();\n    const emailDate = new Date(date);\n    const diffInHours = (now - emailDate) / (1000 * 60 * 60);\n    if (diffInHours < 24) {\n      return emailDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffInHours < 168) {\n      // 7 days\n      return emailDate.toLocaleDateString([], {\n        weekday: 'short'\n      });\n    } else {\n      return emailDate.toLocaleDateString([], {\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n  };\n  const handlePageChange = newPage => {\n    if (newPage >= 1 && newPage <= totalPages) {\n      setCurrentPage(newPage);\n    }\n  };\n  const uniqueLabels = [...new Set(displayEmails.flatMap(email => email.labels))];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 border-b\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Classified Emails\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: fetchExistingEmails,\n            disabled: fetchingEmails,\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n            children: fetchingEmails ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Fetching...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Fetch Gmail Emails\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500\",\n            children: [displayEmails.length, \" emails\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search emails...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            className: \"h-4 w-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedLabel,\n            onChange: e => setSelectedLabel(e.target.value),\n            className: \"border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Labels\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), uniqueLabels.map(label => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: label,\n              children: label.charAt(0).toUpperCase() + label.slice(1)\n            }, label, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divide-y divide-gray-200\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mt-2\",\n          children: \"Loading emails...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center text-red-600\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Error loading emails: \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this) : displayEmails.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(Mail, {\n          className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No emails found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this) : displayEmails.map(email => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 hover:bg-gray-50 transition-colors cursor-pointer ${!email.isRead ? 'bg-blue-50' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  className: \"h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: email.senderName || email.sender\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 23\n                }, this), !email.isRead && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  className: \"h-3 w-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: formatDate(email.timestamp)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `text-sm mb-1 ${!email.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'}`,\n              children: email.subject\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n              children: email.snippet\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-1\",\n              children: email.labels.map((label, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLabelColor(label)}`,\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  className: \"h-3 w-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 25\n                }, this), label]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 15\n        }, this)\n      }, email.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-t flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500\",\n        children: [\"Page \", currentPage, \" of \", totalPages]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handlePageChange(currentPage - 1),\n          disabled: currentPage === 1,\n          className: \"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handlePageChange(currentPage + 1),\n          disabled: currentPage === totalPages,\n          className: \"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(ChevronRight, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n};\n_s(EmailList, \"i33Ib+kmplDdM0Db50lWyHDcirw=\");\n_c = EmailList;\nexport default EmailList;\nvar _c;\n$RefreshReg$(_c, \"EmailList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "emailAPI", "Mail", "Search", "Filter", "ChevronLeft", "ChevronRight", "Tag", "Calendar", "User", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EmailList", "_s", "emails", "setEmails", "loading", "setLoading", "error", "setError", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLabel", "fetchingEmails", "setFetchingEmails", "itemsPerPage", "fetchEmails", "page", "search", "label", "emailData", "getClassifiedEmails", "getEmailsByLabel", "err", "message", "console", "fetchExistingEmails", "result", "processed", "alert", "displayEmails", "getLabelColor", "colors", "work", "personal", "spam", "important", "promotional", "social", "family", "shopping", "tech", "formatDate", "date", "now", "Date", "emailDate", "diffInHours", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "month", "day", "handlePageChange", "newPage", "uniqueLabels", "Set", "flatMap", "email", "labels", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "length", "type", "placeholder", "value", "onChange", "e", "target", "map", "char<PERSON>t", "toUpperCase", "slice", "isRead", "sender<PERSON>ame", "sender", "timestamp", "subject", "snippet", "index", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/projects/AI Mail tagger/src/components/EmailList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { emailAPI } from '../services/api';\nimport { Mail, Search, Filter, ChevronLeft, ChevronRight, Tag, Calendar, User } from 'lucide-react';\n\nconst EmailList = () => {\n  const [emails, setEmails] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLabel, setSelectedLabel] = useState('all');\n  const [fetchingEmails, setFetchingEmails] = useState(false);\n  // const [sortBy, setSortBy] = useState('date'); // TODO: Implement sorting functionality\n\n  const itemsPerPage = 20;\n\n  const fetchEmails = async (page = 1, search = '', label = 'all') => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let emailData;\n      if (label === 'all') {\n        emailData = await emailAPI.getClassifiedEmails(page, itemsPerPage);\n      } else {\n        emailData = await emailAPI.getEmailsByLabel(label);\n      }\n\n      setEmails(emailData.emails || []);\n      setTotalPages(emailData.totalPages || 1);\n      setCurrentPage(page);\n    } catch (err) {\n      setError(err.message);\n      console.error('Error fetching emails:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchEmails(currentPage, searchTerm, selectedLabel);\n  }, [currentPage, searchTerm, selectedLabel]);\n\n  const fetchExistingEmails = async () => {\n    try {\n      setFetchingEmails(true);\n      setError(null);\n\n      const result = await emailAPI.fetchExistingEmails(50, 'is:unread OR newer_than:7d');\n\n      if (result.processed > 0) {\n        // Refresh the email list after fetching\n        await fetchEmails(1, searchTerm, selectedLabel);\n        alert(`Successfully processed ${result.processed} emails from Gmail!`);\n      } else {\n        alert('No new emails found to process.');\n      }\n    } catch (err) {\n      setError(`Failed to fetch emails: ${err.message}`);\n      console.error('Error fetching existing emails:', err);\n    } finally {\n      setFetchingEmails(false);\n    }\n  };\n\n  // Use only real emails from the backend - no sample data fallback\n  const displayEmails = emails;\n\n  const getLabelColor = (label) => {\n    const colors = {\n      work: 'bg-blue-100 text-blue-800',\n      personal: 'bg-green-100 text-green-800',\n      spam: 'bg-red-100 text-red-800',\n      important: 'bg-yellow-100 text-yellow-800',\n      promotional: 'bg-purple-100 text-purple-800',\n      social: 'bg-pink-100 text-pink-800',\n      family: 'bg-indigo-100 text-indigo-800',\n      shopping: 'bg-orange-100 text-orange-800',\n      tech: 'bg-cyan-100 text-cyan-800',\n    };\n    return colors[label] || 'bg-gray-100 text-gray-800';\n  };\n\n  const formatDate = (date) => {\n    const now = new Date();\n    const emailDate = new Date(date);\n    const diffInHours = (now - emailDate) / (1000 * 60 * 60);\n\n    if (diffInHours < 24) {\n      return emailDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    } else if (diffInHours < 168) { // 7 days\n      return emailDate.toLocaleDateString([], { weekday: 'short' });\n    } else {\n      return emailDate.toLocaleDateString([], { month: 'short', day: 'numeric' });\n    }\n  };\n\n  const handlePageChange = (newPage) => {\n    if (newPage >= 1 && newPage <= totalPages) {\n      setCurrentPage(newPage);\n    }\n  };\n\n  const uniqueLabels = [...new Set(displayEmails.flatMap(email => email.labels))];\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border\">\n      {/* Header */}\n      <div className=\"p-6 border-b\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Classified Emails</h2>\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={fetchExistingEmails}\n              disabled={fetchingEmails}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n            >\n              {fetchingEmails ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  <span>Fetching...</span>\n                </>\n              ) : (\n                <>\n                  <Mail className=\"h-4 w-4\" />\n                  <span>Fetch Gmail Emails</span>\n                </>\n              )}\n            </button>\n            <span className=\"text-sm text-gray-500\">\n              {displayEmails.length} emails\n            </span>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          {/* Search */}\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search emails...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n            />\n          </div>\n\n          {/* Label Filter */}\n          <div className=\"flex items-center space-x-2\">\n            <Filter className=\"h-4 w-4 text-gray-400\" />\n            <select\n              value={selectedLabel}\n              onChange={(e) => setSelectedLabel(e.target.value)}\n              className=\"border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n            >\n              <option value=\"all\">All Labels</option>\n              {uniqueLabels.map((label) => (\n                <option key={label} value={label}>\n                  {label.charAt(0).toUpperCase() + label.slice(1)}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Email List */}\n      <div className=\"divide-y divide-gray-200\">\n        {loading ? (\n          <div className=\"p-8 text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"></div>\n            <p className=\"text-gray-500 mt-2\">Loading emails...</p>\n          </div>\n        ) : error ? (\n          <div className=\"p-8 text-center text-red-600\">\n            <p>Error loading emails: {error}</p>\n          </div>\n        ) : displayEmails.length === 0 ? (\n          <div className=\"p-8 text-center text-gray-500\">\n            <Mail className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n            <p>No emails found</p>\n          </div>\n        ) : (\n          displayEmails.map((email) => (\n            <div\n              key={email.id}\n              className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${\n                !email.isRead ? 'bg-blue-50' : ''\n              }`}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <User className=\"h-4 w-4 text-gray-400\" />\n                      <span className=\"text-sm font-medium text-gray-900\">\n                        {email.senderName || email.sender}\n                      </span>\n                      {!email.isRead && (\n                        <div className=\"w-2 h-2 bg-primary-600 rounded-full\"></div>\n                      )}\n                    </div>\n                    <div className=\"flex items-center text-gray-500\">\n                      <Calendar className=\"h-3 w-3 mr-1\" />\n                      <span className=\"text-xs\">{formatDate(email.timestamp)}</span>\n                    </div>\n                  </div>\n\n                  <h3 className={`text-sm mb-1 ${\n                    !email.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'\n                  }`}>\n                    {email.subject}\n                  </h3>\n\n                  <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                    {email.snippet}\n                  </p>\n\n                  <div className=\"flex flex-wrap gap-1\">\n                    {email.labels.map((label, index) => (\n                      <span\n                        key={index}\n                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLabelColor(label)}`}\n                      >\n                        <Tag className=\"h-3 w-3 mr-1\" />\n                        {label}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"p-4 border-t flex items-center justify-between\">\n          <div className=\"text-sm text-gray-500\">\n            Page {currentPage} of {totalPages}\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => handlePageChange(currentPage - 1)}\n              disabled={currentPage === 1}\n              className=\"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n            >\n              <ChevronLeft className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => handlePageChange(currentPage + 1)}\n              disabled={currentPage === totalPages}\n              className=\"p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n            >\n              <ChevronRight className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default EmailList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpG,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D;;EAEA,MAAMiC,YAAY,GAAG,EAAE;EAEvB,MAAMC,WAAW,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,EAAE,EAAEC,KAAK,GAAG,KAAK,KAAK;IAClE,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIgB,SAAS;MACb,IAAID,KAAK,KAAK,KAAK,EAAE;QACnBC,SAAS,GAAG,MAAMpC,QAAQ,CAACqC,mBAAmB,CAACJ,IAAI,EAAEF,YAAY,CAAC;MACpE,CAAC,MAAM;QACLK,SAAS,GAAG,MAAMpC,QAAQ,CAACsC,gBAAgB,CAACH,KAAK,CAAC;MACpD;MAEAnB,SAAS,CAACoB,SAAS,CAACrB,MAAM,IAAI,EAAE,CAAC;MACjCS,aAAa,CAACY,SAAS,CAACb,UAAU,IAAI,CAAC,CAAC;MACxCD,cAAc,CAACW,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZnB,QAAQ,CAACmB,GAAG,CAACC,OAAO,CAAC;MACrBC,OAAO,CAACtB,KAAK,CAAC,wBAAwB,EAAEoB,GAAG,CAAC;IAC9C,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDnB,SAAS,CAAC,MAAM;IACdiC,WAAW,CAACX,WAAW,EAAEI,UAAU,EAAEE,aAAa,CAAC;EACrD,CAAC,EAAE,CAACN,WAAW,EAAEI,UAAU,EAAEE,aAAa,CAAC,CAAC;EAE5C,MAAMe,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFZ,iBAAiB,CAAC,IAAI,CAAC;MACvBV,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMuB,MAAM,GAAG,MAAM3C,QAAQ,CAAC0C,mBAAmB,CAAC,EAAE,EAAE,4BAA4B,CAAC;MAEnF,IAAIC,MAAM,CAACC,SAAS,GAAG,CAAC,EAAE;QACxB;QACA,MAAMZ,WAAW,CAAC,CAAC,EAAEP,UAAU,EAAEE,aAAa,CAAC;QAC/CkB,KAAK,CAAC,0BAA0BF,MAAM,CAACC,SAAS,qBAAqB,CAAC;MACxE,CAAC,MAAM;QACLC,KAAK,CAAC,iCAAiC,CAAC;MAC1C;IACF,CAAC,CAAC,OAAON,GAAG,EAAE;MACZnB,QAAQ,CAAC,2BAA2BmB,GAAG,CAACC,OAAO,EAAE,CAAC;MAClDC,OAAO,CAACtB,KAAK,CAAC,iCAAiC,EAAEoB,GAAG,CAAC;IACvD,CAAC,SAAS;MACRT,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMgB,aAAa,GAAG/B,MAAM;EAE5B,MAAMgC,aAAa,GAAIZ,KAAK,IAAK;IAC/B,MAAMa,MAAM,GAAG;MACbC,IAAI,EAAE,2BAA2B;MACjCC,QAAQ,EAAE,6BAA6B;MACvCC,IAAI,EAAE,yBAAyB;MAC/BC,SAAS,EAAE,+BAA+B;MAC1CC,WAAW,EAAE,+BAA+B;MAC5CC,MAAM,EAAE,2BAA2B;MACnCC,MAAM,EAAE,+BAA+B;MACvCC,QAAQ,EAAE,+BAA+B;MACzCC,IAAI,EAAE;IACR,CAAC;IACD,OAAOT,MAAM,CAACb,KAAK,CAAC,IAAI,2BAA2B;EACrD,CAAC;EAED,MAAMuB,UAAU,GAAIC,IAAI,IAAK;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IAChC,MAAMI,WAAW,GAAG,CAACH,GAAG,GAAGE,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAExD,IAAIC,WAAW,GAAG,EAAE,EAAE;MACpB,OAAOD,SAAS,CAACE,kBAAkB,CAAC,EAAE,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACjF,CAAC,MAAM,IAAIH,WAAW,GAAG,GAAG,EAAE;MAAE;MAC9B,OAAOD,SAAS,CAACK,kBAAkB,CAAC,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC/D,CAAC,MAAM;MACL,OAAON,SAAS,CAACK,kBAAkB,CAAC,EAAE,EAAE;QAAEE,KAAK,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAU,CAAC,CAAC;IAC7E;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC,IAAIA,OAAO,IAAI,CAAC,IAAIA,OAAO,IAAIjD,UAAU,EAAE;MACzCD,cAAc,CAACkD,OAAO,CAAC;IACzB;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC5B,aAAa,CAAC6B,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC;EAE/E,oBACEnE,OAAA;IAAKoE,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAEnDrE,OAAA;MAAKoE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BrE,OAAA;QAAKoE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDrE,OAAA;UAAIoE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EzE,OAAA;UAAKoE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CrE,OAAA;YACE0E,OAAO,EAAE1C,mBAAoB;YAC7B2C,QAAQ,EAAExD,cAAe;YACzBiD,SAAS,EAAC,2IAA2I;YAAAC,QAAA,EAEpJlD,cAAc,gBACbnB,OAAA,CAAAE,SAAA;cAAAmE,QAAA,gBACErE,OAAA;gBAAKoE,SAAS,EAAC;cAA2D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjFzE,OAAA;gBAAAqE,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACxB,CAAC,gBAEHzE,OAAA,CAAAE,SAAA;cAAAmE,QAAA,gBACErE,OAAA,CAACT,IAAI;gBAAC6E,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BzE,OAAA;gBAAAqE,QAAA,EAAM;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eAC/B;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACTzE,OAAA;YAAMoE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACpCjC,aAAa,CAACwC,MAAM,EAAC,SACxB;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzE,OAAA;QAAKoE,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9CrE,OAAA;UAAKoE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrE,OAAA,CAACR,MAAM;YAAC4E,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FzE,OAAA;YACE6E,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,kBAAkB;YAC9BC,KAAK,EAAEhE,UAAW;YAClBiE,QAAQ,EAAGC,CAAC,IAAKjE,aAAa,CAACiE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CX,SAAS,EAAC;UAAuH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNzE,OAAA;UAAKoE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CrE,OAAA,CAACP,MAAM;YAAC2E,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CzE,OAAA;YACE+E,KAAK,EAAE9D,aAAc;YACrB+D,QAAQ,EAAGC,CAAC,IAAK/D,gBAAgB,CAAC+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAClDX,SAAS,EAAC,0GAA0G;YAAAC,QAAA,gBAEpHrE,OAAA;cAAQ+E,KAAK,EAAC,KAAK;cAAAV,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACtCV,YAAY,CAACoB,GAAG,CAAE1D,KAAK,iBACtBzB,OAAA;cAAoB+E,KAAK,EAAEtD,KAAM;cAAA4C,QAAA,EAC9B5C,KAAK,CAAC2D,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG5D,KAAK,CAAC6D,KAAK,CAAC,CAAC;YAAC,GADpC7D,KAAK;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzE,OAAA;MAAKoE,SAAS,EAAC,0BAA0B;MAAAC,QAAA,EACtC9D,OAAO,gBACNP,OAAA;QAAKoE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrE,OAAA;UAAKoE,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FzE,OAAA;UAAGoE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,GACJhE,KAAK,gBACPT,OAAA;QAAKoE,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3CrE,OAAA;UAAAqE,QAAA,GAAG,wBAAsB,EAAC5D,KAAK;QAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,GACJrC,aAAa,CAACwC,MAAM,KAAK,CAAC,gBAC5B5E,OAAA;QAAKoE,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CrE,OAAA,CAACT,IAAI;UAAC6E,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDzE,OAAA;UAAAqE,QAAA,EAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,GAENrC,aAAa,CAAC+C,GAAG,CAAEjB,KAAK,iBACtBlE,OAAA;QAEEoE,SAAS,EAAE,yDACT,CAACF,KAAK,CAACqB,MAAM,GAAG,YAAY,GAAG,EAAE,EAChC;QAAAlB,QAAA,eAEHrE,OAAA;UAAKoE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,eAC/CrE,OAAA;YAAKoE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrE,OAAA;cAAKoE,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CrE,OAAA;gBAAKoE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CrE,OAAA,CAACF,IAAI;kBAACsE,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1CzE,OAAA;kBAAMoE,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAChDH,KAAK,CAACsB,UAAU,IAAItB,KAAK,CAACuB;gBAAM;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,EACN,CAACP,KAAK,CAACqB,MAAM,iBACZvF,OAAA;kBAAKoE,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC3D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNzE,OAAA;gBAAKoE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CrE,OAAA,CAACH,QAAQ;kBAACuE,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCzE,OAAA;kBAAMoE,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAErB,UAAU,CAACkB,KAAK,CAACwB,SAAS;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzE,OAAA;cAAIoE,SAAS,EAAE,gBACb,CAACF,KAAK,CAACqB,MAAM,GAAG,6BAA6B,GAAG,2BAA2B,EAC1E;cAAAlB,QAAA,EACAH,KAAK,CAACyB;YAAO;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAELzE,OAAA;cAAGoE,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EACnDH,KAAK,CAAC0B;YAAO;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAEJzE,OAAA;cAAKoE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAClCH,KAAK,CAACC,MAAM,CAACgB,GAAG,CAAC,CAAC1D,KAAK,EAAEoE,KAAK,kBAC7B7F,OAAA;gBAEEoE,SAAS,EAAE,uEAAuE/B,aAAa,CAACZ,KAAK,CAAC,EAAG;gBAAA4C,QAAA,gBAEzGrE,OAAA,CAACJ,GAAG;kBAACwE,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC/BhD,KAAK;cAAA,GAJDoE,KAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA7CDP,KAAK,CAAC4B,EAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8CV,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL5D,UAAU,GAAG,CAAC,iBACbb,OAAA;MAAKoE,SAAS,EAAC,gDAAgD;MAAAC,QAAA,gBAC7DrE,OAAA;QAAKoE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,OAChC,EAAC1D,WAAW,EAAC,MAAI,EAACE,UAAU;MAAA;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACNzE,OAAA;QAAKoE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CrE,OAAA;UACE0E,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAAClD,WAAW,GAAG,CAAC,CAAE;UACjDgE,QAAQ,EAAEhE,WAAW,KAAK,CAAE;UAC5ByD,SAAS,EAAC,wGAAwG;UAAAC,QAAA,eAElHrE,OAAA,CAACN,WAAW;YAAC0E,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACTzE,OAAA;UACE0E,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAAClD,WAAW,GAAG,CAAC,CAAE;UACjDgE,QAAQ,EAAEhE,WAAW,KAAKE,UAAW;UACrCuD,SAAS,EAAC,wGAAwG;UAAAC,QAAA,eAElHrE,OAAA,CAACL,YAAY;YAACyE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrE,EAAA,CArQID,SAAS;AAAA4F,EAAA,GAAT5F,SAAS;AAuQf,eAAeA,SAAS;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}